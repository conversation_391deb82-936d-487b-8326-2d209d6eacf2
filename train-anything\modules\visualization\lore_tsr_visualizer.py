#!/usr/bin/env python3
"""
LORE-TSR专用可视化器
基于train-anything框架的可视化系统扩展

Time: 2025-07-20
Author: Migration from LORE-TSR to train-anything
Description: 实现LORE-TSR特有的可视化功能，包括逻辑坐标、四点边界框、角点箭头等
"""

import torch
from typing import Any, Optional, Dict, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class LoreTsrVisualizer:
    """
    LORE-TSR专用可视化器
    
    负责在验证阶段生成LORE-TSR模型预测结果的可视化图片，包括：
    - 四点边界框可视化
    - 逻辑坐标标注
    - 角点箭头绘制
    - Processor输出可视化
    - 热力图可视化
    """
    
    def __init__(self, config: Any, device: torch.device, weight_dtype: torch.dtype):
        """
        初始化LORE-TSR可视化器
        
        Args:
            config: 配置对象，包含visualization配置节
            device: 计算设备（GPU/CPU）
            weight_dtype: 权重数据类型
        """
        self.config = config
        self.device = device
        self.weight_dtype = weight_dtype
        
        # 可视化配置
        self.vis_config = getattr(config, 'visualization', None)
        if self.vis_config is None:
            logger.warning("配置文件中缺少visualization配置节，可视化功能将被禁用")
            self.enabled = False
            return
        
        # 基础配置
        self.enabled = getattr(self.vis_config, 'enabled', False)
        self.sample_images_dir = getattr(self.vis_config, 'sample_images_dir', 'assets/test_images')
        self.max_samples = getattr(self.vis_config, 'max_samples', 10)
        self.frequency = getattr(self.vis_config, 'frequency', 1)
        self.debugger_theme = getattr(self.vis_config, 'debugger_theme', 'white')
        
        # 处理output_dir配置：如果为null，则使用basic.output_dir/visualization_results
        vis_output_dir = getattr(self.vis_config, 'output_dir', None)
        if vis_output_dir is None:
            basic_output_dir = getattr(config.basic, 'output_dir', '/tmp/lore_tsr_training_output')
            self.output_dir = f"{basic_output_dir}/visualization_results"
        else:
            self.output_dir = vis_output_dir
        
        # LORE-TSR特有配置
        lore_tsr_config = getattr(self.vis_config, 'lore_tsr', {})
        self.show_logic_coordinates = getattr(lore_tsr_config, 'show_logic_coordinates', True)
        self.show_corner_arrows = getattr(lore_tsr_config, 'show_corner_arrows', True)
        self.show_processor_output = getattr(lore_tsr_config, 'show_processor_output', True)
        self.use_4ps_bbox = getattr(lore_tsr_config, 'use_4ps_bbox', True)
        
        # 样式配置
        style_config = getattr(self.vis_config, 'style', {})
        self.bbox_color = getattr(style_config, 'bbox_color', [0, 255, 0])
        self.logic_text_color = getattr(style_config, 'logic_text_color', [255, 255, 0])
        self.corner_arrow_colors = getattr(style_config, 'corner_arrow_colors', [
            [0, 0, 255],    # 右箭头 - 红色
            [0, 255, 0],    # 下箭头 - 绿色
            [255, 0, 0],    # 左箭头 - 蓝色
            [0, 0, 0]       # 上箭头 - 黑色
        ])
        self.transparency = getattr(style_config, 'transparency', 0.8)
        self.line_thickness = getattr(style_config, 'line_thickness', 2)
        self.arrow_thickness = getattr(style_config, 'arrow_thickness', 1)
        self.text_size = getattr(style_config, 'text_size', 0.3)
        
        # 热力图配置
        heatmap_config = getattr(self.vis_config, 'heatmap', {})
        self.colormap = getattr(heatmap_config, 'colormap', 'jet')
        self.normalize = getattr(heatmap_config, 'normalize', True)
        self.threshold = getattr(heatmap_config, 'threshold', 0.1)
        
        # 可视化计数器
        self.visualization_counter = 0
        
        logger.info("LORE-TSR可视化器已初始化")
        logger.info(f"  - 可视化启用状态: {self.enabled}")
        logger.info(f"  - 样本图片目录: {self.sample_images_dir}")
        logger.info(f"  - 输出目录: {self.output_dir}")
        logger.info(f"  - 可视化频率: {self.frequency}")
        logger.info(f"  - LORE-TSR特有功能: 逻辑坐标={self.show_logic_coordinates}, "
                   f"角点箭头={self.show_corner_arrows}, 四点边界框={self.use_4ps_bbox}")
    
    def visualize_validation_samples(
        self,
        model: torch.nn.Module,
        global_step: int,
        accelerator: Any,
        processor: Optional[Any] = None
    ) -> None:
        """
        可视化验证样本的主入口函数
        基于LORE-TSR的调试可视化逻辑

        Args:
            model: 训练好的LORE-TSR模型
            global_step: 当前训练步数
            accelerator: accelerate框架对象
            processor: LORE-TSR Processor组件（可选）
        """
        if not self._check_visualization_enabled():
            logger.debug("可视化功能未启用，跳过")
            return

        if not self._should_visualize(global_step):
            logger.debug(f"步数{global_step}不需要可视化，跳过")
            return

        logger.info(f"开始执行步数{global_step}的LORE-TSR可视化（第{self.visualization_counter + 1}次）")

        try:
            # 导入必要的工具
            from modules.visualization.lore_tsr_image_utils import LoreTsrImageUtils
            from modules.utils.lore_tsr.visualization_utils import VisualizationUtils

            # 解析可视化配置
            vis_config = VisualizationUtils.parse_visualization_config(self.config)

            # 准备可视化样本
            sample_images = self._prepare_visualization_samples()
            if not sample_images:
                logger.warning("没有找到可视化样本图片")
                return

            # 创建输出目录
            self._create_output_directories(global_step)

            # 设置模型为评估模式
            model.eval()

            # 处理每个样本图片
            for i, image_path in enumerate(sample_images):
                if i >= self.max_samples:
                    break

                try:
                    # 处理单个样本
                    result_image = self.process_single_sample(
                        image_path, model, processor, vis_config
                    )

                    if result_image is not None:
                        # 保存可视化结果
                        output_path = f"{self.output_dir}/step_{global_step}_sample_{i}.png"
                        result_image.save(output_path)
                        logger.debug(f"保存可视化结果: {output_path}")

                except Exception as e:
                    logger.error(f"处理样本{image_path}失败: {e}")
                    continue

            logger.info(f"步数{global_step}的LORE-TSR可视化完成")
            self.visualization_counter += 1

        except Exception as e:
            logger.error(f"可视化过程失败: {e}")
        finally:
            # 恢复模型训练模式
            model.train()
    
    def _check_visualization_enabled(self) -> bool:
        """检查可视化功能是否启用"""
        if self.vis_config is None:
            return False
        return self.enabled
    
    def _should_visualize(self, global_step: int) -> bool:
        """检查当前步数是否需要执行可视化"""
        if not self._check_visualization_enabled():
            return False
        
        # 检查可视化频率
        return (self.visualization_counter + 1) % self.frequency == 0
    
    def process_single_sample(
        self,
        image_path: str,
        model: torch.nn.Module,
        processor: Optional[Any],
        vis_config: Dict[str, Any]
    ) -> Optional[Any]:
        """
        处理单个样本的完整可视化流程
        基于LORE-TSR的show_results逻辑

        Args:
            image_path: 图片路径
            model: LORE-TSR模型
            processor: LORE-TSR Processor组件
            vis_config: 可视化配置

        Returns:
            Optional[Image.Image]: 组合可视化图片
        """
        try:
            from modules.visualization.lore_tsr_image_utils import LoreTsrImageUtils
            from modules.utils.lore_tsr.visualization_utils import VisualizationUtils

            # 加载和预处理图像
            original_image, processed_tensor, meta = LoreTsrImageUtils.load_and_preprocess_image(image_path)

            # 准备模型输入
            if processed_tensor.dim() == 3:
                processed_tensor = processed_tensor.unsqueeze(0)  # 添加batch维度

            processed_tensor = processed_tensor.to(self.device, dtype=self.weight_dtype)

            # 模型推理
            with torch.no_grad():
                model_outputs = model(processed_tensor)

            # 转换模型输出为可视化格式
            predictions = VisualizationUtils.convert_model_output_to_predictions(
                model_outputs, meta
            )

            # 验证预测格式
            if not VisualizationUtils.validate_prediction_format(predictions):
                logger.warning(f"预测格式验证失败: {image_path}")
                return None

            # Processor处理（如果可用）
            logic_axis_outputs = None
            if processor is not None and self.show_processor_output:
                try:
                    # 使用Processor处理模型输出
                    logic_axis_outputs = processor(model_outputs, batch=None)

                    # 转换Processor输出
                    if logic_axis_outputs is not None:
                        logic_coords = VisualizationUtils.convert_processor_output_to_logic_coords(
                            logic_axis_outputs
                        )
                        # 更新预测结果中的逻辑坐标
                        if logic_coords:
                            predictions['logic_coords'] = logic_coords[:len(predictions['bboxes'])]

                except Exception as e:
                    logger.warning(f"Processor处理失败: {e}")

            # 创建组合可视化
            combined_image = self.create_combined_visualization(
                original_image, predictions, logic_axis_outputs, vis_config
            )

            return combined_image

        except Exception as e:
            logger.error(f"处理单个样本失败: {e}")
            return None

    def create_combined_visualization(
        self,
        original_image: Any,
        predictions: Dict[str, Any],
        logic_axis_outputs: Optional[Any],
        vis_config: Dict[str, Any]
    ) -> Any:
        """
        创建包含原图、预测结果、逻辑坐标和热力图的组合可视化图片
        基于LORE-TSR的debugger可视化逻辑

        Args:
            original_image: 原始图像
            predictions: 预测结果字典
            logic_axis_outputs: Processor输出的逻辑轴向信息
            vis_config: 可视化配置

        Returns:
            Image.Image: 组合可视化图片
        """
        try:
            from modules.visualization.lore_tsr_image_utils import LoreTsrImageUtils
            from PIL import Image

            # 获取样式配置
            style_config = vis_config.get('style', {})

            # 绘制预测结果
            result_image = LoreTsrImageUtils.draw_lore_tsr_predictions(
                original_image, predictions, style_config
            )

            # 创建逻辑轴向可视化（如果启用）
            logic_vis_image = None
            if logic_axis_outputs is not None and self.show_processor_output:
                logic_vis_image = LoreTsrImageUtils.create_logic_axis_visualization(
                    logic_axis_outputs, original_image.size
                )

            # 组合图片
            if logic_vis_image is not None:
                # 水平组合原图+预测结果+逻辑可视化
                total_width = result_image.width + logic_vis_image.width
                max_height = max(result_image.height, logic_vis_image.height)

                combined = Image.new('RGB', (total_width, max_height), (255, 255, 255))
                combined.paste(result_image, (0, 0))
                combined.paste(logic_vis_image, (result_image.width, 0))

                return combined
            else:
                return result_image

        except Exception as e:
            logger.error(f"创建组合可视化失败: {e}")
            return original_image

    def _prepare_visualization_samples(self) -> List[str]:
        """准备可视化样本图片列表"""
        try:
            sample_dir = Path(self.sample_images_dir)
            if not sample_dir.exists():
                logger.warning(f"样本图片目录不存在: {sample_dir}")
                return []

            # 支持的图片格式
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}

            # 收集图片文件
            image_files = []
            for ext in image_extensions:
                image_files.extend(sample_dir.glob(f'*{ext}'))
                image_files.extend(sample_dir.glob(f'*{ext.upper()}'))

            # 转换为字符串路径并排序
            image_paths = [str(path) for path in image_files]
            image_paths.sort()

            logger.debug(f"找到{len(image_paths)}个样本图片")
            return image_paths[:self.max_samples]

        except Exception as e:
            logger.error(f"准备可视化样本失败: {e}")
            return []

    def _create_output_directories(self, global_step: Optional[int] = None) -> None:
        """创建输出目录"""
        try:
            output_dir = Path(self.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"创建输出目录: {output_dir}")
        except Exception as e:
            logger.error(f"创建输出目录失败: {e}")
    
    def postprocess_predictions(
        self,
        model_outputs: Dict[str, Any],
        meta: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        后处理模型预测结果，转换为可视化所需格式
        
        Args:
            model_outputs: 模型输出字典
            meta: 图像元信息
            
        Returns:
            标准化预测结果
        """
        # TODO: 步骤9.3将实现具体的后处理逻辑
        logger.debug("后处理预测结果（待实现）")
        return {}
    
    def _create_output_directories(self) -> None:
        """创建输出目录"""
        output_path = Path(self.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        logger.debug(f"创建输出目录: {output_path}")
